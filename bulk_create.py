import csv
import firebase_admin
from firebase_admin import auth, credentials
import re

# Initialize Firebase
cred = credentials.Certificate("firebase_app.json")
firebase_admin.initialize_app(cred)
# Open the CSV file
users = []
with open('users.csv', 'r') as csvfile:
    reader = csv.DictReader(csvfile)
    for row in reader:
        phone_number = row['phone_number']
        username = row['username']

        # Clean the phone number
        if phone_number:
            phone_number = re.sub(r'^\+?91', '', phone_number)
            phone_number = re.sub(r'^0', '', phone_number)

            # Create a user record
            user = auth.ImportUserRecord(
                uid=None,
                phone_number=phone_number,
                display_name=username
            )
            users.append(user)

# Create bulk users in Firebase
result = auth.import_users(users, hash_algorithm=None)

# Retrieve the bulk users at a time
batch_size = 1000
for i in range(0, len(result.users), batch_size):
    batch = result.users[i:i+batch_size]
    user_ids = [user.uid for user in batch]
    users_data = auth.get_users(user_ids)
    for user_data in users_data:
        print(f"User ID: {user_data.uid} - Display Name: {user_data.display_name} - Phone Number: {user_data.phone_number}")

# Store the customer IDs
with open('customer_ids.csv', 'w', newline='') as customer_ids_file:
    writer = csv.writer(customer_ids_file)
    for i, user_result in enumerate(result.users):
        if user_result.status.code == auth.UserImportResult.Status.SUCCESS:
            customer_id = user_result.uid
            writer.writerow([customer_id, users[i].display_name, users[i].phone_number])
        else:
            print(f"Error creating user: {user_result.status.code} - {users[i].display_name} - {users[i].phone_number}")

print("Done!")