#!/usr/bin/env python3
"""
MySQL to Firestore Bulk Import Script
=====================================

This script imports data from MySQL database to Firestore in bulk.
It handles both Firebase Auth user creation and Firestore document creation.

Requirements:
- MySQL database with user data
- Firebase project with Firestore enabled
- Firebase service account key file

Usage:
    python mysql_to_firestore_bulk_import.py

Environment Variables:
    MYSQL_HOST - MySQL host (default: localhost)
    MYSQL_USER - MySQL username
    MYSQL_PASSWORD - MySQL password
    MYSQL_DATABASE - MySQL database name
    FIREBASE_SERVICE_ACCOUNT_KEY_PATH - Path to Firebase service account JSON
"""

import os
import sys
import json
import re
import csv
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

# Third-party imports
import mysql.connector
from mysql.connector import Error
import firebase_admin
from firebase_admin import auth, firestore, credentials
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bulk_import.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class MySQLToFirestoreMigrator:
    """Handles bulk migration from MySQL to Firestore"""
    
    def __init__(self):
        self.mysql_connection = None
        self.firestore_db = None
        self.batch_size = 500  # Firebase Auth import limit
        self.firestore_batch_size = 500  # Firestore batch write limit
        
        # Initialize Firebase
        self._initialize_firebase()
        
        # MySQL connection config
        self.mysql_config = {
            'host': os.getenv('MYSQL_HOST', 'localhost'),
            'user': os.getenv('MYSQL_USER'),
            'password': os.getenv('MYSQL_PASSWORD'),
            'database': os.getenv('MYSQL_DATABASE'),
            'port': int(os.getenv('MYSQL_PORT', 3306)),
            'charset': 'utf8mb4',
            'use_unicode': True,
            'autocommit': True
        }
    
    def _initialize_firebase(self):
        """Initialize Firebase Admin SDK"""
        try:
            if not firebase_admin._apps:
                service_account_path = os.getenv('FIREBASE_SERVICE_ACCOUNT_KEY_PATH')
                
                if not service_account_path or not os.path.exists(service_account_path):
                    # Fallback to firebase_app.json
                    service_account_path = 'firebase_app.json'
                
                if not os.path.exists(service_account_path):
                    raise ValueError(
                        "Firebase service account key not found. "
                        "Set FIREBASE_SERVICE_ACCOUNT_KEY_PATH or ensure firebase_app.json exists."
                    )
                
                cred = credentials.Certificate(service_account_path)
                firebase_admin.initialize_app(cred)
                logger.info("Firebase initialized successfully")
            
            self.firestore_db = firestore.client()
            
        except Exception as e:
            logger.error(f"Failed to initialize Firebase: {e}")
            sys.exit(1)
    
    def connect_mysql(self):
        """Establish MySQL connection"""
        try:
            self.mysql_connection = mysql.connector.connect(**self.mysql_config)
            if self.mysql_connection.is_connected():
                logger.info("Successfully connected to MySQL database")
                return True
        except Error as e:
            logger.error(f"Error connecting to MySQL: {e}")
            return False
    
    def disconnect_mysql(self):
        """Close MySQL connection"""
        if self.mysql_connection and self.mysql_connection.is_connected():
            self.mysql_connection.close()
            logger.info("MySQL connection closed")
    
    def normalize_phone_number(self, phone: str) -> str:
        """Normalize phone number to international format"""
        if not phone:
            return None
        
        # Remove all non-digit characters
        phone = re.sub(r'[^\d]', '', str(phone))
        
        # Remove leading zeros
        phone = phone.lstrip('0')
        
        # Add country code if not present (assuming India +91)
        if not phone.startswith('91') and len(phone) == 10:
            phone = '91' + phone
        elif phone.startswith('91') and len(phone) == 12:
            pass  # Already has country code
        else:
            logger.warning(f"Invalid phone number format: {phone}")
            return None
        
        return '+' + phone
    
    def fetch_users_from_mysql(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Fetch users from MySQL database
        
        Modify this query based on your MySQL table structure
        """
        try:
            cursor = self.mysql_connection.cursor(dictionary=True)
            
            # MODIFY THIS QUERY BASED ON YOUR TABLE STRUCTURE
            query = """
            SELECT 
                id,
                username,
                phone_number,
                email,
                first_name,
                last_name,
                created_at,
                updated_at,
                is_active
            FROM users 
            WHERE phone_number IS NOT NULL 
            AND phone_number != ''
            """
            
            if limit:
                query += f" LIMIT {limit}"
            
            cursor.execute(query)
            users = cursor.fetchall()
            cursor.close()
            
            logger.info(f"Fetched {len(users)} users from MySQL")
            return users
            
        except Error as e:
            logger.error(f"Error fetching users from MySQL: {e}")
            return []
    
    def prepare_firebase_auth_users(self, mysql_users: List[Dict[str, Any]]) -> List[auth.ImportUserRecord]:
        """Prepare user records for Firebase Auth import"""
        firebase_users = []
        
        for user in mysql_users:
            try:
                phone_number = self.normalize_phone_number(user.get('phone_number'))
                if not phone_number:
                    logger.warning(f"Skipping user {user.get('id')} - invalid phone number")
                    continue
                
                # Create display name
                display_name = user.get('username') or user.get('first_name', 'Customer')
                if user.get('last_name'):
                    display_name += f" {user.get('last_name')}"
                
                # Create Firebase Auth user record
                firebase_user = auth.ImportUserRecord(
                    uid=None,  # Let Firebase generate UID
                    phone_number=phone_number,
                    display_name=display_name,
                    email=user.get('email') if user.get('email') else None,
                    disabled=not user.get('is_active', True)
                )
                
                # Store original MySQL data for later Firestore import
                firebase_user.custom_claims = {
                    'mysql_id': user.get('id'),
                    'original_data': json.dumps(user, default=str)
                }
                
                firebase_users.append(firebase_user)
                
            except Exception as e:
                logger.error(f"Error preparing user {user.get('id')}: {e}")
                continue
        
        logger.info(f"Prepared {len(firebase_users)} users for Firebase Auth import")
        return firebase_users
    
    def import_users_to_firebase_auth(self, firebase_users: List[auth.ImportUserRecord]) -> List[Dict[str, Any]]:
        """Import users to Firebase Auth in batches"""
        imported_users = []
        
        for i in range(0, len(firebase_users), self.batch_size):
            batch = firebase_users[i:i + self.batch_size]
            
            try:
                logger.info(f"Importing batch {i//self.batch_size + 1} ({len(batch)} users)")
                result = auth.import_users(batch)
                
                # Process results
                for j, user_result in enumerate(result.users):
                    original_user = batch[j]
                    
                    if hasattr(user_result, 'uid'):  # Success
                        imported_users.append({
                            'uid': user_result.uid,
                            'phone_number': original_user.phone_number,
                            'display_name': original_user.display_name,
                            'email': original_user.email,
                            'mysql_data': json.loads(original_user.custom_claims.get('original_data', '{}'))
                        })
                        logger.info(f"Successfully imported user: {user_result.uid}")
                    else:
                        logger.error(f"Failed to import user: {original_user.phone_number}")
                
                # Rate limiting
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error importing batch: {e}")
                continue
        
        logger.info(f"Successfully imported {len(imported_users)} users to Firebase Auth")
        return imported_users
    
    def create_firestore_documents(self, imported_users: List[Dict[str, Any]]):
        """Create Firestore documents for imported users"""
        
        for i in range(0, len(imported_users), self.firestore_batch_size):
            batch = imported_users[i:i + self.firestore_batch_size]
            
            try:
                # Use Firestore batch for atomic writes
                firestore_batch = self.firestore_db.batch()
                
                for user in batch:
                    mysql_data = user.get('mysql_data', {})
                    
                    # Prepare user profile data
                    user_data = {
                        "createdAt": datetime.now().isoformat(),
                        "displayName": user.get('display_name', 'Customer'),
                        "email": user.get('email', ''),
                        "isProfileComplete": True,
                        "phoneNumber": user.get('phone_number'),
                        "uid": user.get('uid'),
                        "updatedAt": datetime.now().isoformat(),
                        # Add any additional fields from MySQL
                        "mysqlId": mysql_data.get('id'),
                        "firstName": mysql_data.get('first_name', ''),
                        "lastName": mysql_data.get('last_name', ''),
                        "isActive": mysql_data.get('is_active', True),
                        "originalCreatedAt": str(mysql_data.get('created_at', '')),
                        "originalUpdatedAt": str(mysql_data.get('updated_at', ''))
                    }
                    
                    # Create user profile document: users/{uid}/profile/data
                    profile_ref = (self.firestore_db
                                 .collection("users")
                                 .document(user['uid'])
                                 .collection("profile")
                                 .document("data"))
                    
                    firestore_batch.set(profile_ref, user_data)
                    
                    # Create phone number lookup document: pos_users/{phoneNumber}
                    phone_ref = self.firestore_db.collection("pos_users").document(user['phone_number'])
                    phone_data = {
                        "uid": user['uid'],
                        "createdAt": datetime.now().isoformat()
                    }
                    firestore_batch.set(phone_ref, phone_data)
                
                # Commit the batch
                firestore_batch.commit()
                logger.info(f"Created Firestore documents for batch {i//self.firestore_batch_size + 1}")
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Error creating Firestore documents for batch: {e}")
                continue
    
    def export_results(self, imported_users: List[Dict[str, Any]]):
        """Export import results to CSV"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"imported_users_{timestamp}.csv"
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['uid', 'phone_number', 'display_name', 'email', 'mysql_id']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for user in imported_users:
                    mysql_data = user.get('mysql_data', {})
                    writer.writerow({
                        'uid': user.get('uid'),
                        'phone_number': user.get('phone_number'),
                        'display_name': user.get('display_name'),
                        'email': user.get('email'),
                        'mysql_id': mysql_data.get('id')
                    })
            
            logger.info(f"Export completed: {filename}")
            
        except Exception as e:
            logger.error(f"Error exporting results: {e}")
    
    def run_migration(self, limit: Optional[int] = None):
        """Run the complete migration process"""
        logger.info("Starting MySQL to Firestore migration")
        
        try:
            # Step 1: Connect to MySQL
            if not self.connect_mysql():
                return False
            
            # Step 2: Fetch users from MySQL
            mysql_users = self.fetch_users_from_mysql(limit)
            if not mysql_users:
                logger.warning("No users found in MySQL")
                return False
            
            # Step 3: Prepare Firebase Auth users
            firebase_users = self.prepare_firebase_auth_users(mysql_users)
            if not firebase_users:
                logger.warning("No valid users to import")
                return False
            
            # Step 4: Import to Firebase Auth
            imported_users = self.import_users_to_firebase_auth(firebase_users)
            if not imported_users:
                logger.warning("No users successfully imported to Firebase Auth")
                return False
            
            # Step 5: Create Firestore documents
            self.create_firestore_documents(imported_users)
            
            # Step 6: Export results
            self.export_results(imported_users)
            
            logger.info(f"Migration completed successfully! Imported {len(imported_users)} users")
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return False
        
        finally:
            self.disconnect_mysql()

def main():
    """Main function"""
    print("🚀 MySQL to Firestore Bulk Import Script")
    print("=" * 50)
    
    # Check required environment variables
    required_vars = ['MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_DATABASE']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("\nPlease set the following environment variables:")
        print("- MYSQL_HOST (default: localhost)")
        print("- MYSQL_USER")
        print("- MYSQL_PASSWORD") 
        print("- MYSQL_DATABASE")
        print("- MYSQL_PORT (default: 3306)")
        print("- FIREBASE_SERVICE_ACCOUNT_KEY_PATH")
        sys.exit(1)
    
    # Get optional limit
    limit = None
    if len(sys.argv) > 1:
        try:
            limit = int(sys.argv[1])
            print(f"📊 Limiting import to {limit} users")
        except ValueError:
            print("❌ Invalid limit parameter. Please provide a number.")
            sys.exit(1)
    
    # Run migration
    migrator = MySQLToFirestoreMigrator()
    success = migrator.run_migration(limit)
    
    if success:
        print("\n✅ Migration completed successfully!")
    else:
        print("\n❌ Migration failed. Check logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
